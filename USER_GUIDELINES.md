# AI Agent Development Guidelines

Guidelines for AI agents working on the Codexa gaming library management project. These control agent behavior across all development tasks.

## 🔧 MCP Tools - MANDATORY USAGE

### Tool Priorities (Always Use When Available)
- **Supabase MCP (19 tools)** - ALWAYS use for database operations, migrations, SQL queries
- **shadcn-ui MCP (7 tools)** - ALWAYS use for UI component operations and scaffolding  
- **Playwright MCP (24 tools)** - Use for web automation and testing tasks
- **Context 7 MCP (2 tools)** - Use for enhanced context management
- **Sequential thinking MCP (1 tool)** - Use for complex problem-solving workflows

### MCP Usage Rules
- **NEVER use manual CLI commands** when MCP equivalent exists
- **Database operations** - Use `mcp__supabase__*` tools instead of manual SQL
- **UI components** - Use `mcp__shadcn-ui__*` tools for component management
- **Always check MCP diagnostics** before completing tasks

## 🎯 Core Development Requirements

### Code Quality
- **ALWAYS run `npm run lint`** and fix all TypeScript errors before completing tasks
- **ALWAYS use TypeScript strict mode** - no `any` types allowed
- **ALWAYS use shadcn/ui components** - never create custom UI from scratch
- **ALWAYS use Roboto font** and Lucide React icons
- **ALWAYS implement responsive design** for all components

### Database Operations
- **ALWAYS use Supabase MCP tools** for database operations
- **ALWAYS check existing schema** before making changes via `mcp__supabase__list_tables`
- **ALWAYS use migrations** for schema changes via `mcp__supabase__apply_migration`
- **ALWAYS implement RLS policies** for all user data tables

### Component Architecture
- **ALWAYS use shadcn/ui MCP tools** for component scaffolding
- **ALWAYS follow existing patterns** - check similar components first
- **ALWAYS use React Query** for server state management
- **ALWAYS use React Hook Form + Zod** for form validation
- **ALWAYS implement proper loading states** and error boundaries

## 🚀 Task Completion Checklist

### Before Completing Any Task
1. **Run MCP diagnostics** - `mcp__ide__getDiagnostics` to check for errors
2. **Run linting** - Fix all TypeScript and ESLint issues
3. **Test functionality** - Ensure features work as expected
4. **Check responsive design** - Test on mobile/tablet/desktop
5. **Verify database operations** - Use Supabase MCP tools to validate

### Development Workflow
- **ALWAYS check existing code patterns** before implementing new features  
- **ALWAYS use the established service layer** for business logic
- **ALWAYS implement proper error handling** with user-friendly messages
- **ALWAYS add loading states** for async operations
- **NEVER commit changes** unless explicitly requested

## 📋 Project-Specific Guidelines

### Gaming Library Features
- **Focus on user collection management** - Playing, Completed, Backlog, Wishlist statuses
- **Implement proper game deduplication** - Use Steam App IDs and titles for matching
- **Use gaming-specific APIs** - IGDB, Steam, TheGamesDB for authentic data
- **Prioritize performance** - Implement virtualization for large game collections

### AI Integration
- **Multi-provider support** - OpenAI, DeepSeek, Gemini with graceful fallbacks
- **Context-aware recommendations** - Base suggestions on user's existing collection
- **Gaming-focused prompts** - Tailor AI responses for gaming discussions

### Security & Privacy
- **Encrypt all API keys** - Use AES-256 encryption for storage
- **Implement comprehensive RLS** - Protect all user data with Row Level Security  
- **Never expose sensitive data** - Keep API keys server-side via Edge Functions
- **Handle errors gracefully** - Never expose internal system details to users

## 🎮 Domain-Specific Requirements

### Game Data Management
- **Normalize data across APIs** - Convert all API responses to consistent `Game` interface
- **Implement smart search** - Multi-API search with result deduplication
- **Handle missing data gracefully** - Provide fallbacks for incomplete game information
- **Optimize for gaming workflows** - Prioritize features gamers actually use

### Performance Optimization
- **Use React Window** for large collections (1000+ games)
- **Implement intelligent prefetching** - Predict user actions and preload data
- **Optimize database queries** - Use proper indexing and materialized views
- **Cache API responses** - Reduce external API calls with smart caching

---

*These guidelines ensure consistent, high-quality development of Codexa features while leveraging all available MCP tools for enhanced functionality.*