import { useState, useCallback } from 'react';
import { ImageIcon } from '@/lib/icons';
import { cn } from '@/lib/utils';

interface GameImageProps {
  src?: string | null;
  alt: string;
  className?: string;
  aspectRatio?: 'square' | 'portrait' | 'landscape';
  showFallback?: boolean;
  enableHover?: boolean;
  quality?: 'low' | 'medium' | 'high';
}

export const GameImage = ({ 
  src, 
  alt, 
  className,
  aspectRatio = 'portrait',
  showFallback = true,
  enableHover = true,
  quality = 'medium'
}: GameImageProps) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(true);
  }, []);

  const aspectRatioClass = {
    square: 'aspect-square',
    portrait: 'aspect-[2/3]',
    landscape: 'aspect-[3/2]'
  }[aspectRatio];

  const qualityProps = {
    low: { loading: 'lazy' as const, decoding: 'async' as const },
    medium: { loading: 'lazy' as const, decoding: 'async' as const },
    high: { loading: 'eager' as const, decoding: 'sync' as const }
  }[quality];

  return (
    <div className={cn(
      'relative overflow-hidden rounded-lg glass-surface border border-white/10',
      aspectRatioClass,
      enableHover && 'group-hover:shadow-2xl group-hover:shadow-primary/30 transition-all duration-500 hover:glow-pulse neon-border',
      className
    )}>
      <div className="relative w-full h-full">
        {src && !imageError ? (
          <>
            {!imageLoaded && (
              <div className="absolute inset-0 glass-surface rounded-lg">
                <div className="w-full h-full gradient-surface animate-shimmer" />
              </div>
            )}
            
            <img
              src={src}
              alt={alt}
              className={cn(
                'object-cover w-full h-full transition-premium',
                imageLoaded 
                  ? 'opacity-100 group-hover:scale-110 group-hover:brightness-110 group-hover:saturate-110' 
                  : 'opacity-0 scale-105'
              )}
              onLoad={handleImageLoad}
              onError={handleImageError}
              style={{ imageRendering: 'crisp-edges' }}
              {...qualityProps}
            />
            
            {enableHover && (
              <>
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-premium glass-morphism" />
                <div className="absolute inset-0 bg-gradient-to-br from-white/15 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
                <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
                <div className="absolute inset-0 rounded-lg ring-1 ring-white/20 ring-inset opacity-0 group-hover:opacity-100 transition-premium" />
                <div className="absolute inset-0 rounded-lg ring-1 ring-primary/20 ring-inset opacity-0 group-hover:opacity-100 transition-premium delay-75" />
              </>
            )}
          </>
        ) : showFallback ? (
          <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-muted via-muted/90 to-muted/70 group-hover:from-muted/80 group-hover:to-muted/60 transition-premium">
            <div className="relative mb-3">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-full animate-glow" />
              <ImageIcon className="h-16 w-16 text-muted-foreground/60 animate-pulse relative z-10" />
              <div className="absolute inset-0 box-art-shimmer rounded-full" />
            </div>
            <p className="text-xs text-muted-foreground font-medium tracking-wide">No Cover Art</p>
            <div className="mt-2 w-16 h-0.5 bg-gradient-to-r from-transparent via-muted-foreground/20 to-transparent animate-pulse" />
          </div>
        ) : null}
      </div>
    </div>
  );
};