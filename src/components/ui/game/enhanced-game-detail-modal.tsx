/**
 * Enhanced Game Detail Modal
 * Steam-inspired detailed view with header image and comprehensive game information
 */

import React, { useState, useCallback, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import {
  X,
  Play,
  Heart,
  Star,
  Calendar,
  User,
  Clock,
  Tag,
  Download,
  Share2 as Share,
  ExternalLink,
  Plus,
  Gamepad2
} from '@/lib/icons';
import { UserGameWithDetails } from '@/types/game';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { useGameTags } from '@/hooks/useGameTags';

interface EnhancedGameDetailModalProps {
  gameData: UserGameWithDetails | null;
  isOpen: boolean;
  onClose: () => void;
  onStatusUpdate?: (userGameId: string, status: string) => void;
  onQuickPlay?: (gameData: UserGameWithDetails) => void;
  onAddToWishlist?: (gameData: UserGameWithDetails) => void;
}

export const EnhancedGameDetailModal: React.FC<EnhancedGameDetailModalProps> = ({
  gameData,
  isOpen,
  onClose,
  onStatusUpdate,
  onQuickPlay,
  onAddToWishlist
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'activity'>('overview');

  const game = gameData?.game;
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData?.id || '');
  const { gameTags } = useGameTags(gameData?.id || '');

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const formatPlayTime = (hours?: number) => {
    if (!hours) return 'Not played';
    if (hours < 1) return `${Math.round(hours * 60)} minutes`;
    if (hours < 100) return `${hours.toFixed(1)} hours`;
    return `${Math.round(hours)} hours`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'playing': return 'bg-green-600';
      case 'completed': return 'bg-blue-600';
      case 'paused': return 'bg-yellow-600';
      case 'dropped': return 'bg-red-600';
      case 'wishlist': return 'bg-purple-600';
      default: return 'bg-gray-600';
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      setImageLoaded(false);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !gameData || !game) return null;

  return (
    <div
      className="fixed inset-0 glass-overlay flex items-center justify-center z-50 p-4 animate-fade-in backdrop-blur-xl"
      onClick={handleBackdropClick}
    >
      <Card className="max-w-6xl w-full max-h-[90vh] overflow-hidden glass-card border border-white/20 animate-morph-in neon-border">
        {/* Enhanced Header with Tech Effects */}
        <div className="relative h-80 overflow-hidden">
          {/* Tech Grid Overlay */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20" />
            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/80 to-transparent" />
            <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary/60 to-transparent" />
          </div>

          {/* Background image with enhanced effects */}
          {(getBestCoverImage() || game.cover_image) && (
            <div className="absolute inset-0">
              <img
                src={getBestCoverImage() || game.cover_image}
                alt={game.title}
                className={cn(
                  'w-full h-full object-cover transition-all duration-700 blur-sm scale-110',
                  imageLoaded ? 'opacity-40' : 'opacity-0'
                )}
                onLoad={handleImageLoad}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-black/20" />
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-black/60" />
            </div>
          )}

          {/* Header content */}
          <div className="relative h-full flex items-end p-6">
            <div className="flex items-end gap-6 w-full">
              {/* Game cover */}
              <div className="flex-shrink-0">
                <Card className="overflow-hidden border-gray-600 shadow-2xl">
                  <div className="w-32 h-48 bg-gradient-to-br from-gray-800 to-gray-900 relative">
                    {(getBestCoverImage() || game.cover_image) && (
                      <img
                        src={getBestCoverImage() || game.cover_image}
                        alt={game.title}
                        className="w-full h-full object-cover"
                      />
                    )}
                    {hasCustomArtwork && (
                      <div className="absolute top-1 right-1">
                        <Badge className="bg-orange-600 text-white border-0 text-xs px-1 py-0.5">
                          Custom
                        </Badge>
                      </div>
                    )}
                  </div>
                </Card>
              </div>

              {/* Game info */}
              <div className="flex-1 min-w-0 space-y-3">
                <div>
                  <h1 className="text-4xl font-bold text-white mb-2 leading-tight">
                    {game.title}
                  </h1>
                  <div className="flex items-center gap-3 text-gray-300">
                    {game.developer && (
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>{game.developer}</span>
                      </div>
                    )}
                    {game.release_date && (
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(game.release_date).getFullYear()}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Status and actions */}
                <div className="flex items-center gap-3">
                  <Badge 
                    className={cn(
                      'text-white border-0 px-3 py-1',
                      getStatusColor(gameData.status)
                    )}
                  >
                    {gameData.status}
                  </Badge>

                  <div className="flex gap-2">
                    <Button
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => onQuickPlay?.(gameData)}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Play
                    </Button>

                    <Button
                      variant="secondary"
                      className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
                      onClick={() => onAddToWishlist?.(gameData)}
                    >
                      <Heart className="h-4 w-4 mr-2" />
                      Wishlist
                    </Button>

                    <Button
                      variant="secondary"
                      className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
                    >
                      <Share className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Close button */}
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/10 self-start"
                onClick={onClose}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation tabs */}
        <div className="border-b border-gray-700 bg-[#1e2328]">
          <div className="flex px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'details', label: 'Details' },
              { id: 'activity', label: 'Activity' }
            ].map((tab) => (
              <button
                key={tab.id}
                className={cn(
                  'px-4 py-3 text-sm font-medium border-b-2 transition-colors',
                  activeTab === tab.id
                    ? 'text-blue-400 border-blue-400'
                    : 'text-gray-400 border-transparent hover:text-white hover:border-gray-600'
                )}
                onClick={() => setActiveTab(tab.id as any)}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content area */}
        <CardContent className="p-6 max-h-96 overflow-y-auto bg-[#1e2328] text-white">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Description */}
                {game.description && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">About This Game</h3>
                    <p className="text-gray-300 leading-relaxed">
                      {game.description}
                    </p>
                  </div>
                )}

                {/* Tags */}
                {gameTags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {gameTags.map((tag) => (
                        <Badge 
                          key={tag.id}
                          className="bg-blue-600/20 text-blue-400 border-blue-600/30 hover:bg-blue-600/30 transition-colors"
                        >
                          <Tag className="h-3 w-3 mr-1" />
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Play stats */}
                <Card className="bg-gray-800/50 border-gray-700">
                  <CardHeader className="pb-3">
                    <h3 className="text-lg font-semibold text-white">Your Stats</h3>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Time played:</span>
                      <span className="text-white">{formatPlayTime(gameData.hours_played)}</span>
                    </div>
                    
                    {gameData.personal_rating && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Your rating:</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-current text-yellow-500" />
                          <span className="text-white">{gameData.personal_rating}/10</span>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Status:</span>
                      <Badge className={cn('text-white border-0', getStatusColor(gameData.status))}>
                        {gameData.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Game info */}
                <Card className="bg-gray-800/50 border-gray-700">
                  <CardHeader className="pb-3">
                    <h3 className="text-lg font-semibold text-white">Game Info</h3>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm">
                    {game.developer && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Developer:</span>
                        <span className="text-white">{game.developer}</span>
                      </div>
                    )}
                    
                    {game.publisher && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Publisher:</span>
                        <span className="text-white">{game.publisher}</span>
                      </div>
                    )}

                    {game.release_date && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Release Date:</span>
                        <span className="text-white">
                          {new Date(game.release_date).toLocaleDateString()}
                        </span>
                      </div>
                    )}

                    {game.platforms && game.platforms.length > 0 && (
                      <div>
                        <span className="text-gray-400 block mb-2">Platforms:</span>
                        <div className="flex flex-wrap gap-1">
                          {game.platforms.map((platform, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {platform}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'details' && (
            <div className="text-center py-8 text-gray-400">
              <Gamepad2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Detailed information coming soon...</p>
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="text-center py-8 text-gray-400">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Activity tracking coming soon...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
