import { Card } from '@/components/ui/base/card';
import { Skeleton } from '@/components/ui/base/skeleton';

export function GameCardSkeleton({ index = 0 }: { index?: number }) {
  return (
    <Card className="group overflow-hidden glass-card border border-white/10 animate-fade-in relative"
          style={{ animationDelay: `${index * 0.05}s` }}>
      {/* Tech Border Accent */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent" />

      {/* Cover Image Skeleton */}
      <div className="aspect-[2/3] glass-surface relative overflow-hidden">
        <Skeleton className="w-full h-full animate-shimmer" />

        {/* Top badge skeleton */}
        <div className="absolute top-2 right-2">
          <Skeleton className="h-5 w-12 rounded-full glass-surface" />
        </div>

        {/* Bottom badge skeleton */}
        <div className="absolute bottom-2 right-2">
          <Skeleton className="h-5 w-12 rounded-full glass-surface" />
        </div>

        {/* Title overlay skeleton */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-background/95 via-background/85 to-transparent p-3">
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4 bg-white/20 animate-shimmer" shimmer={false} />
            <div className="flex justify-between">
              <Skeleton className="h-3 w-12 bg-white/20 animate-shimmer" shimmer={false} />
              <Skeleton className="h-3 w-16 bg-white/20 animate-shimmer" shimmer={false} />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

export function GameGridSkeleton({ count = 12 }: { count?: number }) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <GameCardSkeleton key={index} index={index} />
      ))}
    </div>
  );
}