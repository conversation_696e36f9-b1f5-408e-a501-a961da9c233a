import { useState, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { 
  Filter, 
  RotateCcw, 
  Settings,
  Monitor,
  Gamepad2,
  Calendar,
  Star,
  User,
  Building,
  X,
  Plus
} from 'lucide-react';
import { GameFilters } from '@/types/filters';
import { FilterChip } from './FilterChip';
import { PlatformFilterComponent } from './PlatformFilter';
import { GenreFilterComponent } from './GenreFilter';
import { YearFilterComponent } from './YearFilter';
import { RatingFilterComponent } from './RatingFilter';
import { DeveloperFilterComponent } from './DeveloperFilter';
import { PublisherFilterComponent } from './PublisherFilter';
import { TagFilter } from './TagFilter';
import { cn } from '@/lib/utils';

interface HorizontalFilterBarProps {
  filters: GameFilters;
  onUpdateFilter: <T extends keyof GameFilters>(
    filterType: T, 
    update: Partial<GameFilters[T]>
  ) => void;
  onReset: () => void;
  activeFilterCount: number;
  isLoading?: boolean;
  className?: string;
  availablePlatforms?: string[];
}

type FilterSection = 'platforms' | 'genres' | 'year' | 'rating' | 'developer' | 'publisher' | 'tags';

const filterIcons = {
  platforms: Monitor,
  genres: Gamepad2,
  year: Calendar,
  rating: Star,
  developer: User,
  publisher: Building,
  tags: Filter
};

const filterLabels = {
  platforms: 'Platforms',
  genres: 'Genres', 
  year: 'Release Year',
  rating: 'Rating',
  developer: 'Developer',
  publisher: 'Publisher',
  tags: 'Tags'
};

const filterVariants = {
  platforms: 'platform' as const,
  genres: 'genre' as const,
  year: 'year' as const,
  rating: 'rating' as const,
  developer: 'developer' as const,
  publisher: 'publisher' as const,
  tags: 'tags' as const
};

export function HorizontalFilterBar({
  filters,
  onUpdateFilter,
  onReset,
  activeFilterCount,
  isLoading = false,
  className,
  availablePlatforms
}: HorizontalFilterBarProps) {
  const [expandedSection, setExpandedSection] = useState<FilterSection | null>(null);
  const [showAllFilters, setShowAllFilters] = useState(false);

  const toggleSection = useCallback((section: FilterSection) => {
    setExpandedSection(current => current === section ? null : section);
  }, []);

  const getFilterChipData = () => {
    const chips = [];
    
    // Platform filters
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
      chips.push({
        id: 'platforms',
        label: filters.platforms.platforms.length === 1 
          ? filters.platforms.platforms[0] 
          : 'Platforms',
        count: filters.platforms.platforms.length,
        isActive: true,
        variant: 'platform' as const,
        icon: <Monitor className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('platforms', { enabled: false, platforms: [] })
      });
    }

    // Genre filters
    if (filters.genres.enabled && filters.genres.genres.length > 0) {
      chips.push({
        id: 'genres',
        label: filters.genres.genres.length === 1 
          ? filters.genres.genres[0] 
          : 'Genres',
        count: filters.genres.genres.length,
        isActive: true,
        variant: 'genre' as const,
        icon: <Gamepad2 className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('genres', { enabled: false, genres: [] })
      });
    }

    // Year filter
    if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) {
      const yearLabel = filters.year.minYear && filters.year.maxYear 
        ? `${filters.year.minYear}-${filters.year.maxYear}`
        : filters.year.minYear 
          ? `From ${filters.year.minYear}`
          : `Until ${filters.year.maxYear}`;
      
      chips.push({
        id: 'year',
        label: yearLabel,
        isActive: true,
        variant: 'year' as const,
        icon: <Calendar className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('year', { enabled: false, minYear: null, maxYear: null })
      });
    }

    // Rating filter
    if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) {
      const ratingLabel = filters.rating.minRating && filters.rating.maxRating 
        ? `${filters.rating.minRating}-${filters.rating.maxRating} Rating`
        : filters.rating.minRating 
          ? `${filters.rating.minRating}+ Rating`
          : `≤${filters.rating.maxRating} Rating`;
      
      chips.push({
        id: 'rating',
        label: ratingLabel,
        isActive: true,
        variant: 'rating' as const,
        icon: <Star className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('rating', { enabled: false, minRating: null, maxRating: null })
      });
    }

    // Developer filter
    if (filters.developer.enabled && filters.developer.developers.length > 0) {
      chips.push({
        id: 'developer',
        label: filters.developer.developers.length === 1 
          ? filters.developer.developers[0] 
          : 'Developers',
        count: filters.developer.developers.length,
        isActive: true,
        variant: 'developer' as const,
        icon: <User className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('developer', { enabled: false, developers: [] })
      });
    }

    // Publisher filter
    if (filters.publisher.enabled && filters.publisher.publishers.length > 0) {
      chips.push({
        id: 'publisher',
        label: filters.publisher.publishers.length === 1 
          ? filters.publisher.publishers[0] 
          : 'Publishers',
        count: filters.publisher.publishers.length,
        isActive: true,
        variant: 'publisher' as const,
        icon: <Building className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('publisher', { enabled: false, publishers: [] })
      });
    }

    // Custom tags filter
    if (filters.customTags.enabled && (filters.customTags.tags.length > 0 || filters.customTags.includeUntagged)) {
      const tagCount = filters.customTags.tags.length + (filters.customTags.includeUntagged ? 1 : 0);
      chips.push({
        id: 'tags',
        label: tagCount === 1 ? 'Tag' : 'Tags',
        count: tagCount,
        isActive: true,
        variant: 'tags' as const,
        icon: <Filter className="h-3 w-3" />,
        onRemove: () => onUpdateFilter('customTags', { enabled: false, tags: [], includeUntagged: false })
      });
    }

    return chips;
  };

  const activeChips = getFilterChipData();
  const availableFilters: FilterSection[] = ['platforms', 'genres', 'year', 'rating', 'developer', 'publisher', 'tags'];
  const inactiveFilters = availableFilters.filter(filter => 
    !activeChips.some(chip => chip.id === filter)
  );

  const renderFilterComponent = (section: FilterSection) => {
    const components = {
      platforms: (
        <PlatformFilterComponent
          filter={filters.platforms}
          onChange={(update) => onUpdateFilter('platforms', update)}
          disabled={isLoading}
          availablePlatforms={availablePlatforms}
        />
      ),
      genres: (
        <GenreFilterComponent
          filter={filters.genres}
          onChange={(update) => onUpdateFilter('genres', update)}
          disabled={isLoading}
        />
      ),
      year: (
        <YearFilterComponent
          filter={filters.year}
          onChange={(update) => onUpdateFilter('year', update)}
          disabled={isLoading}
        />
      ),
      rating: (
        <RatingFilterComponent
          filter={filters.rating}
          onChange={(update) => onUpdateFilter('rating', update)}
          disabled={isLoading}
        />
      ),
      developer: (
        <DeveloperFilterComponent
          filter={filters.developer}
          onChange={(update) => onUpdateFilter('developer', update)}
          disabled={isLoading}
        />
      ),
      publisher: (
        <PublisherFilterComponent
          filter={filters.publisher}
          onChange={(update) => onUpdateFilter('publisher', update)}
          disabled={isLoading}
        />
      ),
      tags: (
        <TagFilter
          filter={filters.customTags}
          onFilterChange={(update) => onUpdateFilter('customTags', update)}
        />
      )
    };

    return components[section];
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Enhanced Main Filter Bar */}
      <Card className="glass-card border border-white/10 hover:border-primary/30 transition-all duration-300 relative">
        {/* Tech Border Accent */}
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/60 to-transparent" />

        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-3 glass-surface border border-primary/30 rounded-xl neon-border animate-glow-pulse">
                <Filter className="h-6 w-6 text-primary" />
              </div>
              <span className="display-text text-xl font-bold text-gradient-primary">Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="glass-surface border border-primary/30 text-primary animate-neon-pulse">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {activeFilterCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onReset}
                  disabled={isLoading}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAllFilters(!showAllFilters)}
                className="text-muted-foreground hover:text-foreground"
              >
                <Settings className="h-4 w-4 mr-1" />
                {showAllFilters ? 'Hide' : 'Show'} All
              </Button>
            </div>
          </div>

          {/* Active Filter Chips */}
          {activeChips.length > 0 && (
            <div className="mb-4">
              <ScrollArea className="w-full">
                <div className="flex items-center gap-2 pb-2">
                  {activeChips.map((chip) => (
                    <FilterChip
                      key={chip.id}
                      label={chip.label}
                      count={chip.count}
                      isActive={chip.isActive}
                      variant={chip.variant}
                      icon={chip.icon}
                      onRemove={chip.onRemove}
                      disabled={isLoading}
                      className="flex-shrink-0"
                    />
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Available Filter Options */}
          <div className="space-y-2">
            <ScrollArea className="w-full">
              <div className="flex items-center gap-2 pb-2">
                {(showAllFilters ? availableFilters : inactiveFilters.slice(0, 4)).map((filterType) => {
                  const Icon = filterIcons[filterType];
                  const isExpanded = expandedSection === filterType;
                  
                  return (
                    <FilterChip
                      key={filterType}
                      label={filterLabels[filterType]}
                      isExpandable={true}
                      isExpanded={isExpanded}
                      variant={filterVariants[filterType]}
                      icon={<Icon className="h-3 w-3" />}
                      onExpand={() => toggleSection(filterType)}
                      disabled={isLoading}
                      className="flex-shrink-0"
                    />
                  );
                })}
                
                {!showAllFilters && inactiveFilters.length > 4 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAllFilters(true)}
                    className="flex items-center gap-1 text-muted-foreground hover:text-foreground px-3 py-1.5 h-auto rounded-full"
                  >
                    <Plus className="h-3 w-3" />
                    +{inactiveFilters.length - 4} more
                  </Button>
                )}
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      </Card>

      {/* Expanded Filter Section */}
      {expandedSection && (
        <Card className="border-primary/20 bg-primary/5 animate-in slide-in-from-top-4 duration-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                {(() => {
                  const Icon = filterIcons[expandedSection];
                  return <Icon className="h-4 w-4 text-primary" />;
                })()}
                <span className="font-medium">{filterLabels[expandedSection]}</span>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setExpandedSection(null)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {renderFilterComponent(expandedSection)}
          </CardContent>
        </Card>
      )}
    </div>
  );
}