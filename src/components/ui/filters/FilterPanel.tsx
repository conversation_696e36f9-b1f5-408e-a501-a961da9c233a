import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Separator } from '@/components/ui/base/separator';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { 
  Filter, 
  RotateCcw, 
  ChevronDown, 
  ChevronUp,
  Save
} from 'lucide-react';
import { GameFilters } from '@/types/filters';
import { PlatformFilterComponent } from './PlatformFilter';
import { GenreFilterComponent } from './GenreFilter';
import { YearFilterComponent } from './YearFilter';
import { RatingFilterComponent } from './RatingFilter';
import { DeveloperFilterComponent } from './DeveloperFilter';
import { PublisherFilterComponent } from './PublisherFilter';
import { cn } from '@/lib/utils';

interface FilterPanelProps {
  filters: GameFilters;
  onUpdateFilter: <T extends keyof GameFilters>(
    filterType: T, 
    update: Partial<GameFilters[T]>
  ) => void;
  onReset: () => void;
  activeFilterCount: number;
  isLoading?: boolean;
  className?: string;
  availablePlatforms?: string[];
}

export function FilterPanel({
  filters,
  onUpdateFilter,
  onReset,
  activeFilterCount,
  isLoading = false,
  className,
  availablePlatforms
}: FilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['platforms']));

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };


  const hasActiveFilters = activeFilterCount > 0;

  return (
    <Card className={cn("w-full glass-card border border-white/10 hover:border-primary/30 transition-all duration-300 relative", className)}>
      {/* Tech Border Accent */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/60 to-transparent" />

      <CardHeader className="pb-4 relative">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 glass-surface border border-primary/30 rounded-lg neon-border">
              <Filter className="h-5 w-5 text-primary" />
            </div>
            <span className="display-text text-lg font-bold text-gradient-primary">Filters</span>
            {hasActiveFilters && (
              <Badge className="glass-surface border border-primary/30 text-primary animate-neon-pulse">
                {activeFilterCount}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                className="h-8 text-muted-foreground hover:text-foreground"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Reset
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && !isExpanded && (
          <div className="flex flex-wrap gap-1 mt-2">
            {filters.platforms.enabled && filters.platforms.platforms.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {filters.platforms.platforms.length} platform{filters.platforms.platforms.length !== 1 ? 's' : ''}
              </Badge>
            )}
            {filters.genres.enabled && filters.genres.genres.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {filters.genres.genres.length} genre{filters.genres.genres.length !== 1 ? 's' : ''}
              </Badge>
            )}
            {filters.year.enabled && (filters.year.minYear || filters.year.maxYear) && (
              <Badge variant="outline" className="text-xs">
                Year: {filters.year.minYear || '?'}-{filters.year.maxYear || '?'}
              </Badge>
            )}
            {filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating) && (
              <Badge variant="outline" className="text-xs">
                Rating: {filters.rating.minRating || 0}-{filters.rating.maxRating || 100}
              </Badge>
            )}
            {filters.developer.enabled && filters.developer.developers.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {filters.developer.developers.length} developer{filters.developer.developers.length !== 1 ? 's' : ''}
              </Badge>
            )}
            {filters.publisher.enabled && filters.publisher.publishers.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {filters.publisher.publishers.length} publisher{filters.publisher.publishers.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <ScrollArea className="h-auto max-h-[600px]">
            <div className="space-y-6">
              
              {/* Platform Filter */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('platforms')}
                  className="w-full justify-between p-0 h-auto font-medium text-sm"
                >
                  <span className="flex items-center gap-2">
                    Platforms
                    {filters.platforms.enabled && filters.platforms.platforms.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {filters.platforms.platforms.length}
                      </Badge>
                    )}
                  </span>
                  {expandedSections.has('platforms') ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {expandedSections.has('platforms') && (
                  <PlatformFilterComponent
                    filter={filters.platforms}
                    onChange={(update) => onUpdateFilter('platforms', update)}
                    disabled={isLoading}
                    availablePlatforms={availablePlatforms}
                  />
                )}
              </div>

              <Separator />

              {/* Genre Filter */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('genres')}
                  className="w-full justify-between p-0 h-auto font-medium text-sm"
                >
                  <span className="flex items-center gap-2">
                    Genres
                    {filters.genres.enabled && filters.genres.genres.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {filters.genres.genres.length}
                      </Badge>
                    )}
                  </span>
                  {expandedSections.has('genres') ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {expandedSections.has('genres') && (
                  <GenreFilterComponent
                    filter={filters.genres}
                    onChange={(update) => onUpdateFilter('genres', update)}
                    disabled={isLoading}
                  />
                )}
              </div>

              <Separator />

              {/* Year Filter */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('year')}
                  className="w-full justify-between p-0 h-auto font-medium text-sm"
                >
                  <span className="flex items-center gap-2">
                    Release Year
                    {filters.year.enabled && (filters.year.minYear || filters.year.maxYear) && (
                      <Badge variant="secondary" className="text-xs">
                        {filters.year.minYear || '?'}-{filters.year.maxYear || '?'}
                      </Badge>
                    )}
                  </span>
                  {expandedSections.has('year') ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {expandedSections.has('year') && (
                  <YearFilterComponent
                    filter={filters.year}
                    onChange={(update) => onUpdateFilter('year', update)}
                    disabled={isLoading}
                  />
                )}
              </div>

              <Separator />

              {/* Rating Filter */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('rating')}
                  className="w-full justify-between p-0 h-auto font-medium text-sm"
                >
                  <span className="flex items-center gap-2">
                    Rating
                    {filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating) && (
                      <Badge variant="secondary" className="text-xs">
                        {filters.rating.minRating || 0}-{filters.rating.maxRating || 100}
                      </Badge>
                    )}
                  </span>
                  {expandedSections.has('rating') ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {expandedSections.has('rating') && (
                  <RatingFilterComponent
                    filter={filters.rating}
                    onChange={(update) => onUpdateFilter('rating', update)}
                    disabled={isLoading}
                  />
                )}
              </div>

              <Separator />

              {/* Developer Filter */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('developer')}
                  className="w-full justify-between p-0 h-auto font-medium text-sm"
                >
                  <span className="flex items-center gap-2">
                    Developer
                    {filters.developer.enabled && filters.developer.developers.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {filters.developer.developers.length}
                      </Badge>
                    )}
                  </span>
                  {expandedSections.has('developer') ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {expandedSections.has('developer') && (
                  <DeveloperFilterComponent
                    filter={filters.developer}
                    onChange={(update) => onUpdateFilter('developer', update)}
                    disabled={isLoading}
                  />
                )}
              </div>

              <Separator />

              {/* Publisher Filter */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={() => toggleSection('publisher')}
                  className="w-full justify-between p-0 h-auto font-medium text-sm"
                >
                  <span className="flex items-center gap-2">
                    Publisher
                    {filters.publisher.enabled && filters.publisher.publishers.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {filters.publisher.publishers.length}
                      </Badge>
                    )}
                  </span>
                  {expandedSections.has('publisher') ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {expandedSections.has('publisher') && (
                  <PublisherFilterComponent
                    filter={filters.publisher}
                    onChange={(update) => onUpdateFilter('publisher', update)}
                    disabled={isLoading}
                  />
                )}
              </div>

              {/* Filter Actions */}
              {hasActiveFilters && (
                <>
                  <Separator />
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      disabled={isLoading}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Preset
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onReset}
                      disabled={isLoading}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Reset All
                    </Button>
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      )}
    </Card>
  );
}