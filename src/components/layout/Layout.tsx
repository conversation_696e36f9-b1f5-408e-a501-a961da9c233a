import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { TooltipProvider } from '../ui/base';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <TooltipProvider>
      <div className="grid min-h-screen w-full md:grid-cols-[280px_1fr] relative overflow-hidden">
        {/* Tech Background with Animated Gradients */}
        <div className="fixed inset-0 -z-10">
          <div className="absolute inset-0 bg-background" />
          <div className="absolute inset-0 gradient-hero opacity-30" />
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent" />
          <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary/50 to-transparent" />
        </div>

        <Sidebar />
        <div className="flex flex-col min-h-screen relative">
          <Header />
          <main className="flex flex-1 flex-col gap-6 p-6 lg:gap-8 lg:p-8 relative">
            {/* Content Container with Glassmorphism */}
            <div className="w-full max-w-none relative">
              <div className="absolute inset-0 glass-surface rounded-2xl opacity-30 -z-10" />
              <div className="relative z-10 animate-fade-in">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>
    </TooltipProvider>
  );
}