import { GameSearch } from '@/components/ui/filters/GameSearch';
import { Game } from '@/types';

interface SearchInterfaceProps {
  onSearchResults: (games: Game[]) => void;
  onLoading: (loading: boolean) => void;
  onError: (error: string | null) => void;
  platformFilters: string[];
}

export function SearchInterface({
  onSearchResults,
  onLoading,
  onError,
  platformFilters
}: SearchInterfaceProps) {
  return (
    <div className="glass-card border border-white/10 hover:border-primary/30 rounded-xl p-8 overflow-visible transition-all duration-300 relative">
      {/* Tech Border Accent */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/60 to-transparent" />
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary/40 to-transparent" />

      <GameSearch
        onSearchResults={onSearchResults}
        onLoading={onLoading}
        onError={onError}
        platformFilters={platformFilters}
      />
    </div>
  );
}