import { Card, CardContent, CardDescription, CardHeader, CardTitle, Badge } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { useDeals } from '@/hooks/usePriceTracking';
import { Flame } from 'lucide-react';

export function HotDealsSection() {
  const { bestDeals, dealStats, isLoadingDeals } = useDeals();

  if (isLoadingDeals) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Flame className="h-5 w-5 text-orange-500" />
            Hot Deals on Your Wishlist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!bestDeals || bestDeals.length === 0) {
    return null;
  }

  return (
    <Card className="glass-card border border-white/10 hover:border-accent/30 transition-all duration-300 hover:glow-pulse relative">
      {/* Tech Border Accent */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent/60 to-transparent" />

      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 glass-surface border border-accent/30 rounded-lg neon-border animate-glow-pulse">
            <Flame className="h-6 w-6 text-accent" />
          </div>
          <span className="display-text text-lg font-bold text-gradient-secondary">Hot Deals on Your Wishlist</span>
        </CardTitle>
        <CardDescription className="text-muted-foreground font-medium">
          {bestDeals.length} deals found • Save up to <span className="text-accent font-bold">{dealStats.biggestDiscount}%</span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {bestDeals.slice(0, 6).map((deal, index) => (
            <div key={index} className="glass-surface border border-white/10 rounded-lg p-4 hover:border-accent/30 transition-all duration-300 hover:glow-pulse">
              <div className="flex items-center justify-between mb-3">
                <p className="font-bold text-sm line-clamp-1">{deal.gameName}</p>
                <Badge className="glass-surface border border-accent/30 text-accent animate-neon-pulse text-xs font-bold">
                  {deal.discountPercent}% OFF
                </Badge>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Now:</span>
                  <span className="font-semibold text-green-600">
                    ${deal.currentPrice.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Was:</span>
                  <span className="line-through text-muted-foreground">
                    ${deal.originalPrice.toFixed(2)}
                  </span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                {deal.shopName}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}