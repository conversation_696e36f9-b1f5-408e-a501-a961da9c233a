import { Badge } from '@/components/ui/base';
import { useAuth } from '@/contexts/AuthContext';
import { TrendingUp } from 'lucide-react';
import { DashboardHeaderProps } from '../types';

export function DashboardHeader({ stats }: DashboardHeaderProps) {
  const { user } = useAuth();

  return (
    <div className="flex items-center justify-between mb-8">
      <div className="space-y-2">
        <h1 className="hero-text text-5xl font-black">
          Dashboard
        </h1>
        <p className="text-muted-foreground text-lg font-medium">
          Welcome back, <span className="text-gradient-primary font-bold">{user?.user_metadata?.username || user?.email}</span>
        </p>
      </div>
      <div className="glass-surface border border-primary/30 rounded-xl p-4 neon-border animate-glow-pulse">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/20 rounded-lg">
            <TrendingUp className="h-5 w-5 text-primary" />
          </div>
          <div>
            <div className="text-2xl font-bold text-primary">
              {stats?.totalGames || 0}
            </div>
            <div className="text-sm text-muted-foreground font-medium">
              games tracked
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}