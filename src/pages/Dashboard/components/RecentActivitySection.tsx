import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON>, Badge } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { useRecentActivity } from '@/hooks/useUserStats';
import { Calendar } from 'lucide-react';
import { RecentActivityItem } from '../types';

export function RecentActivitySection() {
  const { data: recentActivity, isLoading: activityLoading } = useRecentActivity();

  return (
    <Card className="glass-card border border-white/10 hover:border-secondary/30 transition-all duration-300 hover:glow-pulse relative">
      {/* Tech Border Accent */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary/60 to-transparent" />

      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 glass-surface border border-secondary/30 rounded-lg neon-border animate-glow-pulse">
            <Calendar className="h-6 w-6 text-secondary" />
          </div>
          <span className="display-text text-lg font-bold text-gradient-primary">Recent Activity</span>
        </CardTitle>
        <CardDescription className="text-muted-foreground font-medium">Your latest gaming activity</CardDescription>
      </CardHeader>
      <CardContent>
        {activityLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : recentActivity && recentActivity.length > 0 ? (
          (() => {
            const validGames = recentActivity.filter((game: RecentActivityItem) => game.game?.name);
            return validGames.length > 0 ? (
              <div className="space-y-4">
                {validGames.map((game: RecentActivityItem) => (
                  <div key={game.id} className="flex items-center gap-4 p-3 glass-surface border border-white/5 rounded-lg hover:border-secondary/20 transition-all duration-300">
                    <div className="flex-1">
                      <p className="font-bold text-foreground">{game.game?.name}</p>
                      <p className="text-sm text-muted-foreground font-medium">
                        Added to {game.status === 'wishlist' ? 'wishlist' : 'library'}
                      </p>
                    </div>
                    <Badge className="glass-surface border border-secondary/30 text-secondary text-xs font-bold">
                      {game.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No recent activity with valid games</p>
              </div>
            );
          })()
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No recent activity</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}