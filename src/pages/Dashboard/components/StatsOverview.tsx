import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { StatsOverviewProps } from '../types';

export function StatsOverview({ stats }: StatsOverviewProps) {
  if (!stats) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
            </CardHeader>
            <CardContent>
              <LoadingSpinner size="sm" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Games',
      value: stats.totalGames || 0,
      description: 'in your collection',
      colorClass: 'text-primary'
    },
    {
      title: 'Currently Playing',
      value: stats.currentlyPlaying || 0,
      description: 'active games',
      colorClass: 'text-accent'
    },
    {
      title: 'Completed',
      value: stats.completed || 0,
      description: 'finished games',
      colorClass: 'text-success'
    },
    {
      title: 'Wishlist',
      value: stats.wishlistCount || 0,
      description: 'games to play',
      colorClass: 'text-secondary'
    }
  ];

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => (
        <Card key={index} className="glass-card border border-white/10 hover:border-primary/30 transition-all duration-300 hover:glow-pulse relative group">
          {/* Tech Border Accent */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/60 to-transparent" />

          <CardHeader className="pb-4">
            <CardTitle className="text-sm font-bold text-muted-foreground uppercase tracking-wider">
              {stat.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-3xl font-black ${stat.colorClass} group-hover:text-glow transition-all duration-300`}>
              {stat.value}
            </div>
            <p className="text-sm text-muted-foreground font-medium mt-2">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}