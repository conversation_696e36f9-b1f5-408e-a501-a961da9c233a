import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base';
import { CollectionInsightsCard, BacklogOptimizationCard, AIRecommendationsCard } from '@/components/ui/analytics';
import { useDashboardInsights } from '@/hooks/useCollectionInsights';
import { Brain } from 'lucide-react';
import ErrorBoundary from '@/components/layout/ErrorBoundary';

function AIIntelligenceSectionContent() {
  const { 
    collectionStats, 
    overallInsightsScore, 
    averageScore,
    isLoading: insightsLoading 
  } = useDashboardInsights();

  if (insightsLoading || !collectionStats.stats) {
    return null;
  }

  return (
    <>
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Collection Insights */}
        <CollectionInsightsCard className="lg:col-span-2" />
        
        {/* Enhanced AI Score Card */}
        <Card className="glass-card border border-white/10 hover:border-primary/30 transition-all duration-300 hover:glow-pulse relative">
          {/* Tech Border Accent */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/60 to-transparent" />

          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 glass-surface border border-primary/30 rounded-lg neon-border">
                <Brain className="h-6 w-6 text-primary" />
              </div>
              <span className="display-text text-lg font-bold text-gradient-primary">AI Intelligence Score</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-6">
              <div className="hero-text text-5xl font-black text-primary animate-glow-pulse">
                {isNaN(averageScore) ? '0' : averageScore}/100
              </div>
              <div className="space-y-3">
                <div className="flex justify-between text-sm glass-surface p-2 rounded-lg border border-white/5">
                  <span className="font-medium">Collection Health</span>
                  <span className="text-primary font-bold">{Math.round(overallInsightsScore.collectionHealth || 0)}</span>
                </div>
                <div className="flex justify-between text-sm glass-surface p-2 rounded-lg border border-white/5">
                  <span className="font-medium">Backlog Efficiency</span>
                  <span className="text-secondary font-bold">{Math.round(overallInsightsScore.backlogEfficiency || 0)}</span>
                </div>
                <div className="flex justify-between text-sm glass-surface p-2 rounded-lg border border-white/5">
                  <span className="font-medium">Discovery Progress</span>
                  <span className="text-accent font-bold">{Math.round(overallInsightsScore.discoveryProgress || 0)}</span>
                </div>
                <div className="flex justify-between text-sm glass-surface p-2 rounded-lg border border-white/5">
                  <span className="font-medium">Personality Alignment</span>
                  <span className="text-primary font-bold">{Math.round(overallInsightsScore.personalityAlignment || 0)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Recommendations */}
      <AIRecommendationsCard />

      {/* Backlog Optimization */}
      <BacklogOptimizationCard />
    </>
  );
}

export function AIIntelligenceSection() {
  return (
    <ErrorBoundary>
      <AIIntelligenceSectionContent />
    </ErrorBoundary>
  );
}