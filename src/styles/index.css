/* Modern Tech-Forward Design System Entry Point */
@import url("https://fonts.googleapis.com/css2?family=Roboto+Flex:opsz,wght@8..144,100..1000&family=Roboto:wght@400;500;600;700;900&display=swap");
@import "./components.css";
@import "./animations.css";
@import "./accessibility.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Tech-Forward Dark Theme Design System */
    /* Base Colors - Deep space theme */
    --background: 240 10% 3.9%; /* Deep dark background #0a0a0b */
    --foreground: 0 0% 98%; /* Pure white text */

    /* Surface Colors - Layered depth */
    --card: 240 10% 6%; /* Elevated surface #0f0f11 */
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 8%; /* Floating elements #141416 */
    --popover-foreground: 0 0% 98%;

    /* Brand Colors - Tech-inspired palette */
    --primary: 217 91% 60%; /* Electric blue #3b82f6 */
    --primary-foreground: 0 0% 98%;
    --secondary: 142 76% 36%; /* Cyber green #10b981 */
    --secondary-foreground: 0 0% 98%;
    --accent: 47 96% 53%; /* Neon yellow #facc15 */
    --accent-foreground: 240 10% 3.9%;

    /* Neutral Colors - Sophisticated grays */
    --muted: 240 5% 15%; /* Subtle background #262629 */
    --muted-foreground: 240 5% 65%; /* Muted text #a1a1aa */
    --border: 240 6% 20%; /* Subtle borders #323238 */
    --input: 240 6% 20%;
    --ring: 217 91% 60%; /* Focus ring matches primary */

    /* Status Colors - Modern feedback */
    --destructive: 0 84% 60%; /* Vibrant red #ef4444 */
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%; /* Success green #10b981 */
    --success-foreground: 0 0% 98%;
    --warning: 47 96% 53%; /* Warning yellow #facc15 */
    --warning-foreground: 240 10% 3.9%;

    /* Design System Variables */
    --radius: 0.75rem; /* Increased border radius for modern look */

    /* Glassmorphism Variables */
    --glass-bg: 240 10% 6% / 0.8; /* Semi-transparent glass */
    --glass-border: 240 6% 20% / 0.3; /* Subtle glass border */
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Gradient Variables */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(142 76% 36%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(47 96% 53%) 100%);
    --gradient-surface: linear-gradient(135deg, hsl(240 10% 6%) 0%, hsl(240 10% 8%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(217 91% 60% / 0.1) 0%, hsl(142 76% 36% / 0.1) 50%, hsl(47 96% 53% / 0.1) 100%);

    /* Animation Variables */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Theme transition properties */
    --theme-transition-duration: 0.3s;
    --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    /* Enhanced dark mode - even deeper for tech aesthetic */
    --background: 240 10% 2%; /* Ultra deep background #050506 */
    --foreground: 0 0% 98%; /* Pure white text */
    --card: 240 10% 4%; /* Deeper elevated surface #0a0a0b */
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 6%; /* Deeper floating elements #0f0f11 */
    --popover-foreground: 0 0% 98%;
    --primary: 217 91% 65%; /* Brighter electric blue for dark mode */
    --primary-foreground: 0 0% 98%;
    --secondary: 142 76% 40%; /* Brighter cyber green */
    --secondary-foreground: 0 0% 98%;
    --accent: 47 96% 58%; /* Brighter neon yellow */
    --accent-foreground: 240 10% 2%;
    --muted: 240 5% 12%; /* Deeper subtle background */
    --muted-foreground: 240 5% 70%; /* Brighter muted text */
    --border: 240 6% 18%; /* Deeper borders */
    --input: 240 6% 18%;
    --ring: 217 91% 65%;
    --destructive: 0 84% 65%; /* Brighter red for dark mode */
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 40%; /* Brighter success green */
    --success-foreground: 0 0% 98%;
    --warning: 47 96% 58%; /* Brighter warning yellow */
    --warning-foreground: 240 10% 2%;
  }

  .light {
    /* Light mode fallback - minimal changes for desktop-focused app */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 142 76% 36%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 47 96% 53%;
    --accent-foreground: 240 10% 3.9%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;
  }

  /* Modern Typography System */
  .hero-text {
    font-family: 'Roboto Flex', 'Roboto', system-ui, sans-serif;
    font-weight: 900;
    font-variation-settings: 'opsz' 144;
    letter-spacing: -0.02em;
    line-height: 1.1;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .display-text {
    font-family: 'Roboto Flex', 'Roboto', system-ui, sans-serif;
    font-weight: 700;
    font-variation-settings: 'opsz' 72;
    letter-spacing: -0.01em;
    line-height: 1.2;
  }

  .body-text {
    font-family: 'Roboto', system-ui, sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }

  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-glow {
    text-shadow: 0 0 10px hsl(var(--primary) / 0.5);
  }

  .text-tech-glow {
    text-shadow:
      0 0 5px hsl(var(--primary) / 0.8),
      0 0 10px hsl(var(--primary) / 0.6),
      0 0 15px hsl(var(--primary) / 0.4);
  }

  /* Smooth theme transitions */
  * {
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-timing),
      border-color var(--theme-transition-duration) var(--theme-transition-timing),
      color var(--theme-transition-duration) var(--theme-transition-timing),
      fill var(--theme-transition-duration) var(--theme-transition-timing),
      stroke var(--theme-transition-duration) var(--theme-transition-timing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
  }

  /* Disable transitions during theme change to prevent flash */
  .theme-changing * {
    transition: none !important;
  }

  /* Re-enable transitions after theme change */
  .theme-changing.theme-transition-complete * {
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-timing),
      border-color var(--theme-transition-duration) var(--theme-transition-timing),
      color var(--theme-transition-duration) var(--theme-transition-timing),
      fill var(--theme-transition-duration) var(--theme-transition-timing),
      stroke var(--theme-transition-duration) var(--theme-transition-timing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    * {
      transition: none !important;
    }
  }

  /* High contrast mode adjustments */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 20%;
      --ring: 262 100% 70%;
    }

    .dark {
      --border: 0 0% 80%;
      --ring: 262 100% 70%;
    }
  }
}

@layer components {
  /* Enhanced Grid View Styles */
  .enhanced-grid-view {
    @apply relative overflow-hidden;
  }

  .enhanced-grid-view.modern-grid {
    @apply bg-gradient-to-br from-background via-background to-muted/30;
  }

  .enhanced-grid-view.classic-grid {
    @apply bg-background;
  }

  .enhanced-grid-view.minimal-grid {
    @apply bg-background;
  }

  .enhanced-grid-view.vibrant-colors {
    --primary: 262 83% 65%;
    --secondary: 173 58% 45%;
    --accent: 43 96% 62%;
  }

  .enhanced-grid-view.muted-colors {
    --primary: 262 30% 45%;
    --secondary: 173 25% 35%;
    --accent: 43 40% 45%;
  }

  /* Premium Game Card Enhancements */
  .transition-premium {
    @apply transition-all duration-300 ease-out;
  }

  .box-art-glow {
    @apply relative;
  }

  .box-art-glow::before {
    content: "";
    @apply absolute -inset-1 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-lg opacity-0 transition-opacity duration-300 -z-10;
  }

  .box-art-glow:hover::before {
    @apply opacity-100;
  }

  .box-art-shimmer {
    @apply relative overflow-hidden;
  }

  .box-art-shimmer::before {
    content: "";
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent;
    animation: shimmer 2s infinite;
  }

  .glass-morphism {
    @apply bg-gradient-to-t from-black/40 via-black/20 to-transparent;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes glow {
    0%,
    100% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  /* Virtual Scrolling Optimizations */
  .enhanced-grid-view [data-testid="virtual-grid"] {
    @apply will-change-scroll;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .enhanced-grid-view [data-testid="virtual-grid"]::-webkit-scrollbar {
    @apply w-2;
  }

  .enhanced-grid-view [data-testid="virtual-grid"]::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .enhanced-grid-view [data-testid="virtual-grid"]::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  .enhanced-grid-view
    [data-testid="virtual-grid"]::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Responsive Grid Adjustments */
  @media (max-width: 640px) {
    .enhanced-grid-view {
      @apply px-2;
    }
  }

  @media (max-width: 480px) {
    .enhanced-grid-view {
      @apply px-1;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .enhanced-grid-view {
      --primary: 262 100% 70%;
      --secondary: 173 100% 50%;
      --border: 0 0% 50%;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .enhanced-grid-view * {
      @apply transition-none;
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
    }

    .box-art-shimmer::before {
      @apply hidden;
    }

    .animate-glow {
      @apply animate-none;
    }
  }

  /* Focus management for accessibility */
  .enhanced-grid-view:focus-visible {
    @apply outline-2 outline-primary outline-offset-2;
  }

  /* Loading skeleton styles */
  .grid-skeleton {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/60 to-muted;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Roboto", system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    min-height: 100vh;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.8);
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-stagger-fade {
    animation: staggerFade 0.8s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Enhanced box art effects */
  .box-art-shimmer {
    position: relative;
    overflow: hidden;
  }

  .box-art-shimmer::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  .box-art-glow {
    position: relative;
  }

  .box-art-glow::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      45deg,
      hsl(var(--primary) / 0.1),
      hsl(var(--secondary) / 0.1),
      hsl(var(--accent) / 0.1)
    );
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .box-art-glow:hover::after {
    opacity: 1;
  }

  /* Premium glass morphism */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced transitions */
  .transition-premium {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Premium card effects */
  .card-hover-lift {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .card-hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
  }

  .card-parallax {
    transform-style: preserve-3d;
    transition: transform 0.1s ease-out;
  }

  .card-glow-border {
    position: relative;
  }

  .card-glow-border::before {
    content: "";
    position: absolute;
    inset: -1px;
    padding: 1px;
    background: linear-gradient(
      45deg,
      hsl(var(--primary) / 0.3),
      hsl(var(--secondary) / 0.3),
      hsl(var(--accent) / 0.3)
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .card-glow-border:hover::before {
    opacity: 1;
  }

  /* Enhanced image effects */
  .image-zoom-effect {
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .image-zoom-effect:hover {
    transform: scale(1.1);
  }

  /* Staggered animation for card grids */
  .animate-stagger-in {
    animation: staggerIn 0.6s ease-out both;
  }

  .animate-stagger-in:nth-child(1) {
    animation-delay: 0ms;
  }
  .animate-stagger-in:nth-child(2) {
    animation-delay: 100ms;
  }
  .animate-stagger-in:nth-child(3) {
    animation-delay: 200ms;
  }
  .animate-stagger-in:nth-child(4) {
    animation-delay: 300ms;
  }
  .animate-stagger-in:nth-child(5) {
    animation-delay: 400ms;
  }
  .animate-stagger-in:nth-child(6) {
    animation-delay: 500ms;
  }

  /* Premium card interaction effects */
  .card-premium-hover {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .card-premium-hover:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px hsl(var(--primary) / 0.1), 0 0 20px hsl(var(--primary) / 0.1);
  }

  /* Advanced parallax effect */
  .card-3d-transform {
    transform-style: preserve-3d;
    transition: transform 0.1s ease-out;
  }

  /* Premium sparkle animation */
  .animate-sparkle {
    animation: sparkle 1.5s ease-in-out infinite;
  }

  /* Enhanced glow effects */
  .premium-glow {
    position: relative;
  }

  .premium-glow::before {
    content: "";
    position: absolute;
    inset: -2px;
    background: linear-gradient(
      45deg,
      hsl(var(--primary) / 0.2),
      hsl(var(--secondary) / 0.2),
      hsl(var(--accent) / 0.2),
      hsl(var(--primary) / 0.2)
    );
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
    animation: premiumGlowRotate 3s linear infinite;
  }

  .premium-glow:hover::before {
    opacity: 1;
  }

  /* Interactive mouse tracking */
  .mouse-track-gradient {
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      hsl(var(--primary) / 0.1) 0%,
      hsl(var(--secondary) / 0.05) 50%,
      transparent 100%
    );
  }

  /* Enhanced image effects */
  .image-premium-hover {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .image-premium-hover:hover {
    transform: scale(1.1) rotateY(5deg);
    filter: brightness(1.1) saturate(1.2) contrast(1.05);
  }

  /* Premium loading states */
  .premium-shimmer {
    background: linear-gradient(
      90deg,
      hsl(var(--muted)) 0%,
      hsl(var(--muted) / 0.8) 25%,
      hsl(var(--primary) / 0.1) 50%,
      hsl(var(--muted) / 0.8) 75%,
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: premiumShimmer 2s ease-in-out infinite;
  }

  /* Card press effect */
  .card-press-effect {
    transition: transform 0.1s ease-out;
  }

  .card-press-effect:active {
    transform: scale(0.98) translateY(2px);
  }
}
