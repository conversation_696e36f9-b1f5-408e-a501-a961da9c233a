/* Modern Tech-Forward Component Styles */

/* Glassmorphism Base Classes */
.glass-card {
  @apply backdrop-blur-xl border border-white/10;
  background: rgba(15, 15, 17, 0.8);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-surface {
  @apply backdrop-blur-lg border border-white/5;
  background: rgba(10, 10, 11, 0.6);
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.2);
}

.glass-overlay {
  @apply backdrop-blur-md;
  background: rgba(5, 5, 6, 0.9);
}

/* Modern Gradient Backgrounds */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-surface {
  background: var(--gradient-surface);
}

.gradient-hero {
  background: var(--gradient-hero);
}

/* Modern Interactive Effects */
.hover-lift {
  @apply transition-all duration-300 ease-out;
}

.hover-lift:hover {
  @apply -translate-y-1 shadow-2xl;
}

.hover-glow {
  @apply transition-all duration-300 ease-out;
}

.hover-glow:hover {
  @apply shadow-lg;
  box-shadow: 0 10px 25px -5px hsl(var(--primary) / 0.3);
}

.hover-scale {
  @apply transition-transform duration-200 ease-out;
}

.hover-scale:hover {
  @apply scale-105;
}

/* Tech Border Effects */
.tech-border {
  @apply border border-primary/20 relative overflow-hidden;
}

.tech-border::before {
  content: '';
  @apply absolute inset-0 border border-primary/40 rounded-[inherit] opacity-0 transition-opacity duration-300;
}

.tech-border:hover::before {
  @apply opacity-100;
}

.neon-border {
  @apply border border-primary/30 relative;
  box-shadow:
    0 0 5px hsl(var(--primary) / 0.3),
    inset 0 0 5px hsl(var(--primary) / 0.1);
}

.neon-border:hover {
  @apply border-primary/60;
  box-shadow:
    0 0 10px hsl(var(--primary) / 0.5),
    0 0 20px hsl(var(--primary) / 0.3),
    inset 0 0 10px hsl(var(--primary) / 0.2);
}

/* Header Profile Dropdown Styles - Glassmorphism */
.profile-dropdown {
  @apply absolute right-0 mt-2 w-72 rounded-xl shadow-2xl z-[9999] glass-card;
}

/* Avatar Styles */
.avatar-container {
  @apply relative h-10 w-10 rounded-full overflow-hidden;
}

.avatar-image {
  @apply h-full w-full object-cover;
}

.avatar-fallback {
  @apply flex h-full w-full items-center justify-center rounded-full bg-gradient-to-br from-primary via-secondary to-accent text-primary-foreground text-sm font-bold shadow-sm;
}

/* Profile dropdown animations */
.profile-dropdown-enter {
  animation: profileDropdownEnter 0.2s ease-out forwards;
}

/* Modern Card Styles */
.card-modern {
  @apply glass-card hover-lift hover-glow rounded-xl p-6 transition-all duration-300;
}

.card-tech {
  @apply glass-card tech-border rounded-xl p-6 relative overflow-hidden;
}

.card-tech::after {
  content: '';
  @apply absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent;
}

.card-neon {
  @apply glass-card neon-border rounded-xl p-6;
}

/* Enhanced Button Styles */
.btn-tech {
  @apply relative overflow-hidden bg-gradient-to-r from-primary to-secondary text-primary-foreground font-medium px-6 py-3 rounded-lg transition-all duration-300;
}

.btn-tech::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-secondary to-primary opacity-0 transition-opacity duration-300;
}

.btn-tech:hover::before {
  @apply opacity-100;
}

.btn-tech > * {
  @apply relative z-10;
}

.btn-glass {
  @apply glass-surface hover-glow border border-white/10 text-foreground font-medium px-6 py-3 rounded-lg transition-all duration-300;
}

.btn-neon {
  @apply neon-border bg-transparent text-primary font-medium px-6 py-3 rounded-lg transition-all duration-300;
}

/* Enhanced dropdown styling */
.dropdown-header {
  @apply px-4 py-4 border-b border-white/10 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-t-xl glass-surface;
}

.dropdown-menu-item {
  @apply flex items-center gap-3 w-full px-4 py-3 text-sm hover:bg-white/5 hover:text-foreground transition-all duration-200 rounded-lg mx-1 font-medium;
}

.dropdown-menu-item-destructive {
  @apply flex items-center gap-3 w-full px-4 py-3 text-sm hover:bg-destructive/10 hover:text-destructive text-destructive transition-all duration-200 rounded-lg mx-1 font-medium;
}

.dropdown-icon-container {
  @apply p-1.5 bg-primary/10 rounded-md group-hover:bg-primary/20 transition-colors;
}

.dropdown-icon-container-destructive {
  @apply p-1.5 bg-destructive/10 rounded-md group-hover:bg-destructive/20 transition-colors;
}

/* Platform Family Group Animations */
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}