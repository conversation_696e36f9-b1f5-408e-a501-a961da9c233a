/* Modern Tech-Forward Animation System */

/* Enhanced Entrance Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9) rotateX(10deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotateX(0deg);
  }
}

/* Modern Glassmorphism Entrance */
@keyframes glassSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(12px);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Modern Tech Glow Effects */
@keyframes glow {
  from {
    text-shadow: 0 0 5px hsl(var(--primary) / 0.5);
    box-shadow: 0 0 10px hsl(var(--primary) / 0.2);
  }
  to {
    text-shadow: 0 0 20px hsl(var(--primary) / 0.8),
      0 0 30px hsl(var(--primary) / 0.6);
    box-shadow: 0 0 30px hsl(var(--primary) / 0.4),
      0 0 60px hsl(var(--primary) / 0.2);
  }
}

@keyframes techGlow {
  0%, 100% {
    box-shadow:
      0 0 5px hsl(var(--primary) / 0.3),
      inset 0 0 5px hsl(var(--primary) / 0.1);
  }
  50% {
    box-shadow:
      0 0 20px hsl(var(--primary) / 0.6),
      0 0 40px hsl(var(--primary) / 0.3),
      inset 0 0 10px hsl(var(--primary) / 0.2);
  }
}

@keyframes neonPulse {
  0%, 100% {
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  50% {
    border-color: hsl(var(--primary) / 1);
    box-shadow:
      0 0 10px hsl(var(--primary) / 0.6),
      0 0 20px hsl(var(--primary) / 0.4),
      0 0 30px hsl(var(--primary) / 0.2);
  }
}

@keyframes staggerFade {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes boxArtHover {
  0% {
    transform: scale(1) rotateY(0deg);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: scale(1.05) rotateY(2deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }
}

@keyframes premiumGlow {
  0%,
  100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.1);
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3),
      0 0 30px hsl(var(--primary) / 0.2);
  }
}

@keyframes staggerIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes premiumGlowRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes premiumShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes profileDropdownEnter {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Advanced Tech Animations */
@keyframes techSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0px);
  }
}

@keyframes morphIn {
  from {
    opacity: 0;
    transform: scale(0.8) rotateY(45deg);
    filter: blur(8px);
  }
  to {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow:
      0 0 5px hsl(var(--primary) / 0.3),
      0 0 10px hsl(var(--primary) / 0.2);
  }
  50% {
    box-shadow:
      0 0 15px hsl(var(--primary) / 0.6),
      0 0 30px hsl(var(--primary) / 0.4),
      0 0 45px hsl(var(--primary) / 0.2);
  }
}

@keyframes dataStream {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes hologramFlicker {
  0%, 100% {
    opacity: 1;
    filter: brightness(1);
  }
  2% {
    opacity: 0.8;
    filter: brightness(1.2);
  }
  4% {
    opacity: 1;
    filter: brightness(0.9);
  }
  6% {
    opacity: 0.9;
    filter: brightness(1.1);
  }
}